/*  Popup – single rAF batch render, zero reflows afterwards  */

const $ = q => document.querySelector(q);
const DOM = { list: $('#tab-list-container'), all: $('#close-all-duplicates') };

const ESC = (() => { const d = document.createElement('div'); return s=>(d.textContent=s??'',d.innerHTML); })();

let groups = [];   // [[tab]]
const MSG = (a, o) => chrome.runtime.sendMessage({ a, ...o });

/* ---------- helpers ---------- */
const hDomain = new Map();
const domOf = url => {
  if (hDomain.has(url)) return hDomain.get(url);
  let h = 'other'; try { h = new URL(url).hostname.replace(/^www\./i, '') || h; } catch {}
  hDomain.set(url, h); if (hDomain.size > 600) hDomain.delete(hDomain.keys().next().value);
  return h;
};

const hCol = new Map();
const col = s => {
  if (hCol.has(s)) return hCol.get(s);
  let h = 0; for (let i = 0; i < s.length; i++) h = ((h << 5) - h + s.charCodeAt(i)) | 0;
  const c = `hsl(${Math.abs(h) % 360},62%,55%)`;
  hCol.set(s, c); if (hCol.size > 1200) hCol.delete(hCol.keys().next().value);
  return c;
};

/* ---------- render ---------- */
function draw() {
  if (!groups.length) { DOM.list.textContent = '🎉 No duplicate tabs'; DOM.all.disabled = true; return; }

  const frag = new DocumentFragment(), byDom = new Map();
  for (const g of groups) (byDom.get(hDomain(g[0].url)) ?? byDom.set(hDomain(g[0].url), []).get(hDomain(g[0].url))).push(g);

  for (const dom of [...byDom.keys()].sort()) {
    const sets = byDom.get(dom);
    const head = Object.assign(document.createElement('div'), { className: 'domain-group-header', textContent: `${dom} – ${sets.reduce((n, s) => n + s.length, 0)} tabs` });
    frag.append(head);

    for (const set of sets) {
      const c = col(set[0].did);
      for (const tab of set) {
        const item = document.createElement('div');
        item.className = 'tab-item';
        item.dataset.id = tab.id; item.dataset.w = tab.wid;
        item.style.borderLeft = `4px solid ${c}`;
        item.innerHTML = `
          <img src="${ESC(tab.icon || '')}" width="16" height="16" class="tab-icon" loading="lazy">
          <div class="tab-title" title="${ESC(tab.title || '')}\n${ESC(tab.url || '')}">${ESC(tab.title || '(no title)')}</div>`;
        frag.append(item);
      }
      /* close set button */
      const btn = document.createElement('button');
      btn.textContent = 'Close duplicates';
      btn.className = 'close-set-button';
      btn.dataset.did = set[0].did; btn.dataset.keep = set[0].id;
      frag.append(btn);
    }
  }

  requestAnimationFrame(() => { DOM.list.replaceChildren(frag); DOM.all.disabled = false; });
}

/* ---------- load ---------- */
async function load() {
  DOM.list.textContent = 'Loading…'; DOM.all.disabled = true;
  const r = await MSG('ls');
  groups = r?.g ?? [];
  draw();
}

/* ---------- UI actions ---------- */
DOM.list.addEventListener('click', async e => {
  const btn = e.target.closest('.close-set-button');
  if (btn) {
    const set = groups.find(g => g[0].did === btn.dataset.did);
    if (!set) return;
    const ids = set.filter(t => t.id != btn.dataset.keep).map(t => t.id);
    if (ids.length) await MSG('cl', { ids });
    load(); return;
  }
  const item = e.target.closest('.tab-item');
  if (item) { await MSG('sw', { t: +item.dataset.id, w: +item.dataset.w }); window.close(); }
});

DOM.all.addEventListener('click', async () => {
  DOM.all.disabled = true; DOM.list.textContent = 'Closing duplicates…';
  const ids = groups.flatMap(g => g.slice(1).map(t => t.id));  // keep first of each group
  if (ids.length) await MSG('cl', { ids });
  load();
});

document.addEventListener('DOMContentLoaded', load);
