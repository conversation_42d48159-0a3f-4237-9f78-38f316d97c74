:root { --fg:#eee; --bg:#202124; --accent:#d93025; font:14px system-ui; }
body  { margin:0; color:var(--fg); background:var(--bg); width:340px; user-select:none; }
header{ padding:6px 10px; font-weight:600; }
main  { max-height:430px; overflow:auto; }
footer{ padding:4px 10px; font-size:12px; opacity:.8; }
button{ margin:6px 10px 10px; width:calc(100% - 20px); padding:6px; font-weight:600;
        background:var(--accent); border:none; color:#fff; border-radius:4px; cursor:pointer; }
button:disabled{ opacity:.4; cursor:default; }
.domain-group-header{ padding:4px 8px; font-weight:500; background:#303134; }
.tab-item{ display:flex; align-items:center; padding:4px 6px 4px 2px; gap:6px; cursor:pointer; }
.tab-item:hover { background:#2a2b2d; }
.tab-item.is-active{ background:#383a3c; }
.tab-content{ display:flex; align-items:center; gap:6px; flex:1; }
.tab-icon{ flex:none; }
.tab-title{ white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
.tab-url{ font-size:11px; opacity:.7; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
.close-set-button{ background:none; border:none; color:var(--fg); font-size:18px;
                   line-height:12px; padding:0 4px; cursor:pointer; }
.close-set-button:hover{ color:var(--accent); }
