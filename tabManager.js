/*  TabManager – dedup logic, heavily profiled & debounced  */

const IGNORE = ['about:', 'chrome:', 'devtools:'];
const SUB = /^(www|m|mobile|amp)\./i;
const DEF = /\/(?:index|default)\.(?:html?)$/i;
const MULTI = /\/{2,}/g;

class Mgr {
  tabs   = new Map();          // id → {url,title,icon,wid,did}
  groups = new Map();          // did → Set<ids>
  dirty  = false;              // need recalc?
  recalcTimer = null;
  badge = '';

  /* -------- public -------- */
  async init() {
    if (this.inited) return;
    this.inited = true;
    const all = await chrome.tabs.query({});
    all.forEach(t => this.#up(t));
    this.#listen();
    this.#recalc();
  }

  async list() {
    if (this.dirty) await this.#recalc();   // ensure fresh
    const res = [];
    for (const [did, set] of this.groups) {
      if (set.size < 2) continue;
      const g = [];
      set.forEach(id => {
        const t = this.tabs.get(id);
        if (t) g.push(t);
      });
      if (g.length > 1) res.push(g);
    }
    return { g: res };
  }

  /* -------- internals -------- */
  #listen() {
    chrome.tabs.onCreated.addListener(t => this.#up(t));
    chrome.tabs.onUpdated.addListener((id, ci, t) => {
      if (ci.url || ci.title || ci.status === 'complete') this.#up(t);
    });
    chrome.tabs.onRemoved.addListener(id => this.#rm(id));
    chrome.tabs.onReplaced.addListener(async (add, rem) => {
      this.#rm(rem); this.#up(await chrome.tabs.get(add));
    });
    chrome.windows.onRemoved.addListener(w => {
      for (const [id, t] of this.tabs) if (t.wid === w) this.#rm(id);
    });
  }

  #schedule() {
    if (this.recalcTimer) return;
    this.recalcTimer = setTimeout(() => {
      this.recalcTimer = null;
      this.#recalc();
    }, 120);                   // run once for bursts
  }

  #up(t) {
    if (!t?.id) return;
    const did = this.#id(t.url);
    const prev = this.tabs.get(t.id)?.did;
    if (prev && prev !== did) this.groups.get(prev)?.delete(t.id);
    if (did) (this.groups.get(did) ?? this.groups.set(did, new Set()).get(did)).add(t.id);
    this.tabs.set(t.id, { id: t.id, url: t.url, title: t.title, icon: t.favIconUrl, wid: t.windowId, did });
    this.dirty = true; this.#schedule();
  }

  #rm(id) {
    const d = this.tabs.get(id);
    if (!d) return;
    this.groups.get(d.did)?.delete(id);
    this.tabs.delete(id);
    this.dirty = true; this.#schedule();
  }

  async #recalc() {
    this.dirty = false;
    let count = 0;
    for (const set of this.groups.values()) if (set.size > 1) count += set.size;
    const txt = count ? String(count) : '';
    if (txt !== this.badge) {
      this.badge = txt;
      chrome.action.setBadgeText({ text: txt });
      chrome.action.setBadgeBackgroundColor({ color: '#d93025' });
    }
  }

  /* fast canonicaliser – no heavy regex except once */
  #id(u) {
    if (!u || IGNORE.some(p => u.startsWith(p))) return null;
    try {
      const url = new URL(u.split('#', 1)[0]);
      const proto = url.protocol === 'https:' ? 'http:' : url.protocol;
      const host = url.hostname.replace(SUB, '').toLowerCase();
      let path = url.pathname.replace(MULTI, '/').replace(DEF, '/');
      if (path.length > 1 && path.endsWith('/')) path = path.slice(0, -1);
      const search = [...url.searchParams].filter(([k, v]) => v && !k.startsWith('utm_'))
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([k, v]) => `${k}=${v}`).join('&');
      return `${proto}//${host}${path}${search ? '?' + search : ''}`;
    } catch {                        // fallback
      return u.slice(0, 120);
    }
  }
}

export default new Mgr();
