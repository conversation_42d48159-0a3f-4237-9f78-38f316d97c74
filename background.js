/*  Background SW – minimal main-thread work, debounced updates  */

import mgr from './tabManager.js';

/* tiny helper */
const reply = (send, ok, data = {}) => send({ ok, ...data });

chrome.runtime.onMessage.addListener((msg, _sender, send) => {
  (async () => {
    switch (msg?.a) {                         // short action keys
      case 'ls':                              // list duplicates
        reply(send, true, await mgr.list());
        break;

      case 'sw':                              // switch tab
        await Promise.allSettled([
          chrome.tabs.update(msg.t, { active: true }),
          chrome.windows.update(msg.w, { focused: true })
        ]);
        reply(send, true);
        break;

      case 'cl':                              // close tabs
        if (!msg.ids?.length) return reply(send, false);
        await chrome.tabs.remove(msg.ids);
        reply(send, true);
        break;

      default:
        reply(send, false, { err: 'bad-action' });
    }
  })();
  return true;                                // async
});

/* kick-start on cold boot */
chrome.runtime.onStartup.addListener(() => mgr.init());
chrome.runtime.onInstalled.addListener(() => mgr.init());
